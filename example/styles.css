#connect-area {
  display: grid;
  grid-template-columns: 1fr 1fr;
  grid-template-rows: min-content min-content;
  grid-auto-flow: column;
  grid-gap: 10px;
  margin-bottom: 15px;
}

#options-area {
  display: flex;
  flex-wrap: wrap;
  margin-left: 1.25rem;
  margin-right: 1.25rem;
  column-gap: 3rem;
  row-gap: 1rem;
  margin-bottom: 10px;
}

#actions-area {
  display: grid;
  grid-template-columns: fit-content(100px) auto;
  grid-gap: 1.25rem;
  margin-bottom: 15px;
}

#inputs-area {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  grid-gap: 1.25rem;
  margin-bottom: 10px;
}

#chat-input-area {
  margin-top: 1.2rem;
  display: grid;
  grid-template-columns: auto min-content;
  gap: 1.25rem;
}

#screenshare-area {
  position: relative;
  margin-top: 1.25rem;
  margin-bottom: 1.25rem;
  display: none;
}

#screenshare-area video {
  max-width: 900px;
  max-height: 900px;
  border: 3px solid rgba(0, 0, 0, 0.5);
}

#participants-area {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
}

#participants-area > .participant {
  width: 100%;
}

#participants-area > .participant::before {
  content: '';
  display: inline-block;
  width: 1px;
  height: 0;
  padding-bottom: calc(100% / (16 / 9));
}

#log-area {
  margin-top: 1.25rem;
  margin-bottom: 1rem;
}

#log {
  width: 66.6%;
  height: 100px;
}

.participant {
  position: relative;
  padding: 0;
  margin: 0;
  border-radius: 5px;
  border: 3px solid rgba(0, 0, 0, 0);
  overflow: hidden;
}

.participant video {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background-color: #aaa;
  object-fit: cover;
  border-radius: 5px;
}

.participant .info-bar {
  position: absolute;
  width: 100%;
  bottom: 0;
  display: grid;
  color: #eee;
  padding: 2px 8px 2px 8px;
  background-color: rgba(0, 0, 0, 0.35);
  grid-template-columns: minmax(50px, auto) 1fr minmax(50px, auto);
  z-index: 5;
}

.participant .size {
  text-align: center;
}

.participant .right {
  text-align: right;
}

.participant.speaking {
  border: 3px solid rgba(94, 166, 190, 0.7);
}

.participant .mic-off {
  color: #d33;
  text-align: right;
}

.participant .mic-on {
  text-align: right;
}

.participant .connection-excellent {
  color: green;
}

.participant .connection-good {
  color: orange;
}

.participant .connection-poor {
  color: red;
}

.participant .volume-control {
  position: absolute;
  top: 4px;
  right: 2px;
  display: flex;
  z-index: 4;
  height: 100%;
}

.participant .volume-control > input {
  width: 16px;
  height: 40%;
  -webkit-appearance: slider-vertical; /* Chromium */
}

.participant .volume-meter {
  position: absolute;
  z-index: 4;
}
