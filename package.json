{"name": "livekit-client", "version": "1.9.0", "description": "JavaScript/TypeScript client SDK for LiveKit", "main": "./dist/livekit-client.umd.js", "unpkg": "./dist/livekit-client.umd.js", "module": "./dist/livekit-client.esm.mjs", "exports": {"types": "./dist/src/index.d.ts", "import": "./dist/livekit-client.esm.mjs", "require": "./dist/livekit-client.umd.js"}, "files": ["dist", "src"], "types": "dist/src/index.d.ts", "typesVersions": {"<4.8": {"./dist/src/index.d.ts": ["./dist/src/ts4.2/index.d.ts"]}}, "repository": "**************:livekit/client-sdk-js.git", "author": "<PERSON> <<EMAIL>>", "license": "Apache-2.0", "scripts": {"build": "rollup --config --bundleConfigAsCjs && yarn downlevel-dts", "build:watch": "rollup --watch --config rollup.config.js", "build-docs": "typedoc", "proto": "protoc --plugin=node_modules/ts-proto/protoc-gen-ts_proto --ts_proto_opt=esModuleInterop=true --ts_proto_out=./src/proto --ts_proto_opt=outputClientImpl=false,useOptionals=messages,oneof=unions -I./protocol ./protocol/livekit_rtc.proto ./protocol/livekit_models.proto", "sample": "vite serve example --host 0.0.0.0 --port 8082 --open", "lint": "eslint src", "test": "jest", "deploy": "gh-pages -d example/dist", "format": "prettier --write src example/sample.ts", "format:check": "prettier --check src", "release": "yarn build && changeset publish", "downlevel-dts": "downlevel-dts ./dist/ ./dist/ts4.2 --to=4.2"}, "dependencies": {"async-await-queue": "^1.2.1", "events": "^3.3.0", "loglevel": "^1.8.0", "protobufjs": "^7.0.0", "sdp-transform": "^2.14.1", "ts-debounce": "^4.0.0", "typed-emitter": "^2.1.0", "ua-parser-js": "^1.0.2", "webrtc-adapter": "^8.1.1"}, "devDependencies": {"@babel/core": "7.21.4", "@babel/preset-env": "7.21.4", "@changesets/cli": "2.26.1", "@livekit/changesets-changelog-github": "^0.0.4", "@rollup/plugin-babel": "6.0.3", "@rollup/plugin-commonjs": "24.1.0", "@rollup/plugin-json": "6.0.0", "@rollup/plugin-node-resolve": "15.0.2", "@rollup/plugin-terser": "^0.4.0", "@trivago/prettier-plugin-sort-imports": "^4.1.1", "@types/jest": "29.5.0", "@types/sdp-transform": "2.4.6", "@types/ua-parser-js": "0.7.36", "@types/ws": "8.5.4", "@typescript-eslint/eslint-plugin": "5.58.0", "@typescript-eslint/parser": "5.58.0", "downlevel-dts": "^0.11.0", "eslint": "8.38.0", "eslint-config-airbnb-typescript": "17.0.0", "eslint-config-prettier": "8.8.0", "eslint-plugin-import": "2.27.5", "gh-pages": "5.0.0", "jest": "29.5.0", "prettier": "^2.8.8", "rollup": "3.20.2", "rollup-plugin-delete": "^2.0.0", "rollup-plugin-filesize": "10.0.0", "rollup-plugin-re": "1.0.7", "rollup-plugin-typescript2": "0.34.1", "ts-jest": "29.1.0", "ts-proto": "1.146.0", "typedoc": "0.24.1", "typedoc-plugin-no-inherit": "1.4.0", "typescript": "4.9.5", "vite": "4.2.1"}, "browserslist": ["safari >= 11", "ios_saf >= 11", "chrome >= 64", "and_chr >= 64", "android >= 64", "firefox >= 53", "and_ff >= 53", "edge >= 79", "Opera >= 52", "Samsung >= 9.2", "not IE 11", "not dead"]}