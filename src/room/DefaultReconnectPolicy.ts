import type { ReconnectContext, ReconnectPolicy } from './ReconnectPolicy';

const maxRetryDelay = 7000;

const DEFAULT_RETRY_DELAYS_IN_MS = [
  0,
  300,
  2 * 2 * 300,
  3 * 3 * 300,
  4 * 4 * 300,
  max<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  maxR<PERSON>ry<PERSON>elay,
  maxR<PERSON>ryDelay,
  maxRetryDelay,
  maxRetryDelay,
];

class DefaultReconnectPolicy implements ReconnectPolicy {
  private readonly _retryDelays: number[];

  constructor(retryDelays?: number[]) {
    this._retryDelays = retryDelays !== undefined ? [...retryDelays] : DEFAULT_RETRY_DELAYS_IN_MS;
  }

  public nextRetryDelayInMs(context: ReconnectContext): number | null {
    if (context.retryCount >= this._retryDelays.length) return null;

    const retryDelay = this._retryDelays[context.retryCount];
    if (context.retryCount <= 1) return retryDelay;

    return retryDelay + Math.random() * 1_000;
  }
}

export default DefaultReconnectPolicy;
